<?php
/*
 * Template Name: Video
 */

// Enqueue Fancybox CSS và JS
function enqueue_fancybox_assets() {
    // Fancybox CSS
    wp_enqueue_style('fancybox-css', 'https://cdn.jsdelivr.net/npm/@fancyapps/ui@5.0/dist/fancybox/fancybox.css', array(), '5.0.0');
    
    // Fancybox JS
    wp_enqueue_script('fancybox-js', 'https://cdn.jsdelivr.net/npm/@fancyapps/ui@5.0/dist/fancybox/fancybox.umd.js', array(), '5.0.0', true);
}

// Always enqueue cho template này
add_action('wp_enqueue_scripts', 'enqueue_fancybox_assets');

// Function để extract YouTube ID từ URL
function get_youtube_id($url) {
    preg_match('/(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/\s]{11})/', $url, $matches);
    return isset($matches[1]) ? $matches[1] : false;
}

// Function để tạo YouTube embed URL
function get_youtube_embed_url($url) {
    $youtube_id = get_youtube_id($url);
    if ($youtube_id) {
        return 'https://www.youtube.com/embed/' . $youtube_id . '?autoplay=1&rel=0&modestbranding=1&enablejsapi=1&controls=1';
    }
    return $url;
}

// Function để lấy YouTube thumbnail
function get_youtube_thumbnail($url, $quality = 'maxresdefault') {
    $youtube_id = get_youtube_id($url);
    if ($youtube_id) {
        return 'https://img.youtube.com/vi/' . $youtube_id . '/' . $quality . '.jpg';
    }
    return false;
}
?>

<?php get_header();?>
	<div class="main_container col-md-12 col-sm-12 col-xs-12">
		<div class="container">
			<?php
			// Lấy tất cả parent terms của taxonomy 'danhmuc_videos'
			$parent_terms = get_terms(array(
				'taxonomy' => 'danhmuc_videos',
				'parent' => 0, // Chỉ lấy parent terms (cấp 0)
				'hide_empty' => true,
			));

			if (!empty($parent_terms) && !is_wp_error($parent_terms)) {
				foreach ($parent_terms as $parent_term) {
					
					// Lấy tất cả child terms của parent term này
					$child_terms = get_terms(array(
						'taxonomy' => 'danhmuc_videos',
						'parent' => $parent_term->term_id, // Lấy child terms
						'hide_empty' => true,
					));

					// Kiểm tra xem parent term này có child terms có video không
					$has_videos_in_children = false;
					$child_terms_with_videos = array();

					if (!empty($child_terms) && !is_wp_error($child_terms)) {
						foreach ($child_terms as $child_term) {
							// Kiểm tra xem child term này có videos không
							$videos_check = new WP_Query(array(
								'post_type' => 'videos',
								'posts_per_page' => 1, // Chỉ cần kiểm tra có hay không
								'tax_query' => array(
									array(
										'taxonomy' => 'danhmuc_videos',
										'field'    => 'term_id',
										'terms'    => $child_term->term_id,
									),
								),
							));

							if ($videos_check->have_posts()) {
								$has_videos_in_children = true;
								$child_terms_with_videos[] = $child_term;
							}
							wp_reset_postdata();
						}
					}

					// Chỉ hiển thị parent term nếu có ít nhất 1 child term có videos
					if ($has_videos_in_children) {
						// Hiển thị tiêu đề danh mục cha 1 lần
						echo '<h2 class="parent-category-title">' . esc_html($parent_term->name) . '</h2>';

						// Loop qua các child terms có videos
						foreach ($child_terms_with_videos as $child_term) {
							// Hiển thị tiêu đề danh mục con
							echo '<h3 class="child-category-title">' . esc_html($child_term->name) . '</h3>';
							
							// Query posts thuộc danh mục con này
							$videos_query = new WP_Query(array(
								'post_type' => 'videos',
								'posts_per_page' => -1, // Lấy tất cả videos
								'tax_query' => array(
									array(
										'taxonomy' => 'danhmuc_videos',
										'field'    => 'term_id',
										'terms'    => $child_term->term_id,
									),
								),
							));

							if ($videos_query->have_posts()) {
								echo '<div class="videos-grid row">';
								
								while ($videos_query->have_posts()) {
									$videos_query->the_post();
									
									// Lấy custom field cho YouTube URL (điều chỉnh theo field name của bạn)
									$youtube_url = get_post_meta(get_the_ID(), '_video_link', true);
									if (empty($youtube_url)) {
										$youtube_url = get_permalink(); // Fallback nếu không có YouTube URL
									}
									
									// Tạo embed URL cho Fancybox
									$embed_url = get_youtube_embed_url($youtube_url);
									
									// Lấy YouTube thumbnail nếu không có featured image
									$youtube_thumb = get_youtube_thumbnail($youtube_url);
									
									// Tạo unique identifier cho mỗi video
									$video_id = 'video-' . get_the_ID();
									?>
									<div class="col-md-4 col-sm-6 col-xs-12">
										<div class="tg_item_list tg_hv_scale1">
											<div class="wrap_figure">
												<a href="<?php echo esc_url($embed_url); ?>"
												   data-fancybox="video-gallery"
												   data-caption="<?php echo esc_attr(get_the_title()); ?>"
												   data-type="iframe"
												   data-video-id="<?php echo $video_id; ?>"
												   class="tg_link_abs video-link"
												   tabindex="0">
													<?php if (has_post_thumbnail()) : ?>
														<?php the_post_thumbnail('large', array(
															'class' => 'attachment-large size-large wp-post-image',
															'alt' => get_the_title(),
															'decoding' => 'async'
														)); ?>
													<?php elseif ($youtube_thumb) : ?>
														<img src="<?php echo esc_url($youtube_thumb); ?>" 
															 alt="<?php the_title(); ?>" 
															 class="attachment-large size-large wp-post-image">
													<?php else : ?>
														<img src="<?php echo get_template_directory_uri(); ?>/images/default-video.jpg" 
															 alt="<?php the_title(); ?>" 
															 class="attachment-large size-large wp-post-image">
													<?php endif; ?>
												</a>
											</div>
											<div class="tg_info">
												<p><a href="<?php echo esc_url($embed_url); ?>"
													  data-fancybox="video-gallery"
													  data-caption="<?php echo esc_attr(get_the_title()); ?>"
													  data-type="iframe"
													  data-video-id="<?php echo $video_id; ?>"
													  class="video-link"><?php the_title(); ?></a></p>
											</div>
										</div>
									</div>
									<?php
								}
								
								echo '</div>'; // Đóng videos-grid
								
								// Reset post data
								wp_reset_postdata();
							}
							
							// Thêm khoảng cách giữa các danh mục con
							echo '<div style="margin-bottom: 30px;"></div>';
						}
						
						// Thêm khoảng cách lớn giữa các danh mục cha
						echo '<div style="margin-bottom: 50px;"></div>';
					}
				}
			} else {
				echo '<p>Không có danh mục video nào.</p>';
			}
			?>
		</div>
	</div> 

	<!-- Custom CSS cho Fancybox -->
	<style>
	/* Custom styling cho Fancybox */
	.fancybox__container {
		z-index: 999999;
	}

	.fancybox__backdrop {
		background: rgba(0, 0, 0, 0.85);
		cursor: pointer; /* Cho biết có thể click để đóng */
	}

	.fancybox__content {
		background: transparent;
		border-radius: 8px;
		overflow: hidden;
	}

	.fancybox__iframe {
		border-radius: 8px;
		min-height: 315px;
		border: none;
	}

	.fancybox__caption {
		background: rgba(0, 0, 0, 0.8);
		color: white;
		padding: 15px 20px;
		font-size: 16px;
		text-align: center;
		border-radius: 0 0 8px 8px;
	}

	.fancybox__button {
		background: rgba(255, 255, 255, 0.1);
		border: 1px solid rgba(255, 255, 255, 0.2);
		color: white;
		backdrop-filter: blur(10px);
		transition: all 0.3s ease;
	}

	.fancybox__button:hover {
		background: rgba(255, 255, 255, 0.2);
		transform: scale(1.1);
	}

	.fancybox__button--close {
		background: rgba(231, 76, 60, 0.8);
		font-size: 18px;
		font-weight: bold;
	}

	.fancybox__button--close:hover {
		background: rgba(192, 57, 43, 0.9);
		transform: scale(1.1);
	}

	.fancybox-spinner {
		display: flex;
		justify-content: center;
		align-items: center;
		height: 100px;
	}

	.spinner-border {
		width: 3rem;
		height: 3rem;
		border: 0.25em solid transparent;
		border-right-color: #007bff;
		border-radius: 50%;
		animation: spinner-border 0.75s linear infinite;
	}

	@keyframes spinner-border {
		to { transform: rotate(360deg); }
	}

	/* Loading state cho video */
	.video-loading {
		position: relative;
	}

	.video-loading::before {
		content: '';
		position: absolute;
		top: 50%;
		left: 50%;
		transform: translate(-50%, -50%);
		width: 40px;
		height: 40px;
		border: 3px solid rgba(255, 255, 255, 0.3);
		border-top: 3px solid #fff;
		border-radius: 50%;
		animation: spin 1s linear infinite;
		z-index: 10;
	}

	@keyframes spin {
		0% { transform: translate(-50%, -50%) rotate(0deg); }
		100% { transform: translate(-50%, -50%) rotate(360deg); }
	}

	/* Responsive cho Fancybox */
	@media (max-width: 768px) {
		.fancybox__iframe {
			min-height: 250px;
		}

		.fancybox__caption {
			font-size: 14px;
			padding: 10px 15px;
		}

		.fancybox__button {
			width: 44px;
			height: 44px;
		}
	}

	@media (max-width: 480px) {
		.fancybox__iframe {
			min-height: 200px;
		}
	}
	</style>

	<!-- JavaScript khởi tạo Fancybox -->
	<script>
	document.addEventListener('DOMContentLoaded', function() {
		// Biến để lưu iframe hiện tại
		let currentIframe = null;

		// Kiểm tra xem Fancybox đã load chưa
		if (typeof Fancybox !== 'undefined') {
			// Khởi tạo Fancybox cho tất cả video với selector tổng quát
			Fancybox.bind('[data-fancybox="video-gallery"]', {
				// Tự động play video
				autoFocus: true,
				trapFocus: true,
				placeFocusBack: true,

				// Cấu hình cho iframe (YouTube)
				Iframe: {
					autoSize: true,
					preload: false,
					css: {
						width: '100%',
						height: '100%'
					}
				},

				// Animation
				showClass: 'f-fadeIn',
				hideClass: 'f-fadeOut',

				// UI settings
				Toolbar: {
					display: {
						left: [],
						middle: [],
						right: ['close']
					}
				},

				// Close settings
				closeButton: 'top',
				dragToClose: true,

				// Keyboard
				Keyboard: {
					Escape: 'close',
					Delete: 'close',
					Backspace: 'close'
				},

				// Touch
				Touch: {
					vertical: true,
					momentum: true
				},

				// Event handlers
				on: {
					// Khi mở video
					reveal: function(fancybox, slide) {
						console.log('Video opened');
						// Lưu reference đến iframe
						currentIframe = slide.$iframe;
					},

					// Khi đóng video
					destroy: function(fancybox) {
						console.log('Video closed - stopping playback');
						// Dừng video bằng cách reload iframe src
						if (currentIframe && currentIframe[0]) {
							const iframe = currentIframe[0];
							const src = iframe.src;
							iframe.src = 'about:blank';
							setTimeout(() => {
								iframe.src = src.replace('autoplay=1', 'autoplay=0');
							}, 100);
						}
						currentIframe = null;
					},

					// Khi chuyển slide (nếu có nhiều video)
					beforeChange: function(fancybox, slide) {
						// Dừng video trước đó
						if (currentIframe && currentIframe[0]) {
							const iframe = currentIframe[0];
							iframe.src = iframe.src.replace('autoplay=1', 'autoplay=0');
						}
					}
				}
			});

			console.log('Fancybox initialized successfully');
		} else {
			console.error('Fancybox not loaded');

			// Fallback: nếu Fancybox không load, thêm target="_blank"
			document.querySelectorAll('[data-fancybox]').forEach(function(link) {
				link.removeAttribute('data-fancybox');
				link.removeAttribute('data-type');
				link.setAttribute('target', '_blank');
			});
		}
	});

	// Thêm event listener để dừng tất cả video khi trang được unload
	window.addEventListener('beforeunload', function() {
		// Dừng tất cả video YouTube
		const iframes = document.querySelectorAll('iframe[src*="youtube.com"]');
		iframes.forEach(function(iframe) {
			iframe.src = 'about:blank';
		});
	});

	// Thêm event listener cho việc pause video khi click ra ngoài
	document.addEventListener('click', function(e) {
		// Nếu click vào nút close hoặc backdrop của fancybox
		if (e.target.classList.contains('fancybox__backdrop') ||
			e.target.classList.contains('fancybox__button--close')) {
			// Dừng tất cả video
			const iframes = document.querySelectorAll('.fancybox__iframe');
			iframes.forEach(function(iframe) {
				if (iframe.src.includes('youtube.com')) {
					iframe.src = iframe.src.replace('autoplay=1', 'autoplay=0');
				}
			});
		}
	});

	// Thêm script để quản lý video tốt hơn
	function stopAllVideos() {
		// Dừng tất cả iframe YouTube
		const iframes = document.querySelectorAll('iframe[src*="youtube.com"]');
		iframes.forEach(function(iframe) {
			if (iframe.src.includes('autoplay=1')) {
				iframe.src = iframe.src.replace('autoplay=1', 'autoplay=0');
			}
		});

		// Dừng tất cả video HTML5 nếu có
		const videos = document.querySelectorAll('video');
		videos.forEach(function(video) {
			video.pause();
		});
	}

	// Thêm event listener cho ESC key
	document.addEventListener('keydown', function(e) {
		if (e.key === 'Escape') {
			stopAllVideos();
		}
	});

	// Thêm event listener cho visibility change (khi chuyển tab)
	document.addEventListener('visibilitychange', function() {
		if (document.hidden) {
			stopAllVideos();
		}
	});
	</script>



<style>
/* CSS cho Video Template */

/* Container chính */
.main_container {
    padding: 30px 0;
    background: #f8f9fa;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 15px;
}

/* Tiêu đề danh mục cha */
.parent-category-title {
    font-size: 32px;
    font-weight: 700;
    color: #2c3e50;
    margin: 40px 0 25px 0;
    padding: 15px 0;
    border-bottom: 3px solid #e74c3c;
    position: relative;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.parent-category-title:first-child {
    margin-top: 0;
}

.parent-category-title::before {
    content: '';
    position: absolute;
    bottom: -3px;
    left: 0;
    width: 80px;
    height: 3px;
    background: #c0392b;
}

/* Tiêu đề danh mục con */
.child-category-title {
    font-size: 24px;
    font-weight: 600;
    color: #34495e;
    margin: 30px 0 20px 0;
    padding: 10px 0 10px 20px;
    border-left: 4px solid #3498db;
    background: #ffffff;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    border-radius: 0 8px 8px 0;
    position: relative;
}

.child-category-title::before {
    content: '▶';
    color: #3498db;
    margin-right: 10px;
    font-size: 16px;
}

/* Grid layout cho videos */
.videos-grid {
    margin-bottom: 40px;
}

.videos-grid .col-md-4 {
    margin-bottom: 25px;
}

/* Item video */
.tg_item_list {
    background: #ffffff;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    border: 1px solid #e9ecef;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.tg_item_list:hover {
    transform: translateY(-8px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

/* Wrapper cho hình ảnh */
.wrap_figure {
    position: relative;
    overflow: hidden;
    border-radius: 12px 12px 0 0;
    flex-shrink: 0;
}

.wrap_figure::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0,0,0,0);
    transition: all 0.3s ease;
    z-index: 2;
}

.tg_item_list:hover .wrap_figure::before {
    background: rgba(0,0,0,0.2);
}

/* Link và hình ảnh */
.tg_link_abs {
    display: block;
    position: relative;
    overflow: hidden;
}

.wp-post-image {
    width: 100%;
    height: 200px;
    object-fit: cover;
    transition: all 0.3s ease;
    display: block;
}

.tg_item_list:hover .wp-post-image {
    transform: scale(1.05);
}

/* Play button overlay */
.tg_link_abs::after {
    content: '▶';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(231, 76, 60, 0.9);
    color: white;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    opacity: 1;
    transition: all 0.3s ease;
    z-index: 3;
}

.tg_item_list:hover .tg_link_abs::after {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1.1);
}

/* Thông tin video */
.tg_info {
    padding: 20px;
    flex-grow: 1;
    display: flex;
    align-items: center;
}

.tg_info p {
    margin: 0;
    font-size: 16px;
    line-height: 1.4;
}

.tg_info a {
    color: #2c3e50;
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s ease;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.tg_info a:hover {
    color: #e74c3c;
    text-decoration: none;
}

/* Responsive */
@media (max-width: 768px) {
    .parent-category-title {
        font-size: 28px;
        margin: 30px 0 20px 0;
    }
    
    .child-category-title {
        font-size: 20px;
        margin: 25px 0 15px 0;
        padding: 8px 0 8px 15px;
    }
    
    .videos-grid .col-md-4 {
        margin-bottom: 20px;
    }
    
    .wp-post-image {
        height: 180px;
    }
    
    .tg_info {
        padding: 15px;
    }
    
    .tg_info p {
        font-size: 14px;
    }
}

@media (max-width: 480px) {
    .main_container {
        padding: 20px 0;
    }
    
    .container {
        padding: 0 10px;
    }
    
    .parent-category-title {
        font-size: 24px;
    }
    
    .child-category-title {
        font-size: 18px;
    }
    
    .wp-post-image {
        height: 160px;
    }
    
    .tg_link_abs::after {
        width: 50px;
        height: 50px;
        font-size: 16px;
    }
}

/* Animation cho load */
.tg_item_list {
    animation: fadeInUp 0.6s ease forwards;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Stagger animation cho các items */
.videos-grid .col-md-4:nth-child(1) .tg_item_list { animation-delay: 0.1s; }
.videos-grid .col-md-4:nth-child(2) .tg_item_list { animation-delay: 0.2s; }
.videos-grid .col-md-4:nth-child(3) .tg_item_list { animation-delay: 0.3s; }
.videos-grid .col-md-4:nth-child(4) .tg_item_list { animation-delay: 0.4s; }
.videos-grid .col-md-4:nth-child(5) .tg_item_list { animation-delay: 0.5s; }
.videos-grid .col-md-4:nth-child(6) .tg_item_list { animation-delay: 0.6s; }
</style>


<?php get_footer();?>